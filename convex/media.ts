import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all media files with pagination and filtering
export const getAllMedia = query({
  args: {
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    mimeType: v.optional(v.string()),
    uploadedBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    let query = ctx.db.query("media");
    
    // Apply filters
    if (args.uploadedBy) {
      query = query.withIndex("by_uploaded_by", (q) => q.eq("uploadedBy", args.uploadedBy));
    }
    
    if (args.tags && args.tags.length > 0) {
      query = query.withIndex("by_tags", (q) => q.eq("tags", args.tags[0])); // Simple tag filtering
    }
    
    query = query.order("desc");
    
    const results = await query.take(limit);
    
    // Filter by mime type if specified
    const filteredResults = args.mimeType 
      ? results.filter(media => media.mimeType.startsWith(args.mimeType))
      : results;
    
    return filteredResults;
  },
});

// Get media by ID
export const getMediaById = query({
  args: { id: v.id("media") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Search media files
export const searchMedia = query({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const searchTerm = args.query.toLowerCase();
    
    const allMedia = await ctx.db.query("media").collect();
    
    const filteredMedia = allMedia.filter(media => 
      media.filename.toLowerCase().includes(searchTerm) ||
      media.originalName.toLowerCase().includes(searchTerm) ||
      media.alt?.toLowerCase().includes(searchTerm) ||
      media.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
    
    return filteredMedia.slice(0, limit);
  },
});

// Upload media file
export const uploadMedia = mutation({
  args: {
    filename: v.string(),
    originalName: v.string(),
    mimeType: v.string(),
    size: v.number(),
    url: v.string(),
    alt: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    
    const now = Date.now();
    
    return await ctx.db.insert("media", {
      filename: args.filename,
      originalName: args.originalName,
      mimeType: args.mimeType,
      size: args.size,
      url: args.url,
      alt: args.alt,
      tags: args.tags || [],
      uploadedBy: "system", // TODO: Get from auth
      createdAt: now,
    });
  },
});

// Update media metadata
export const updateMedia = mutation({
  args: {
    id: v.id("media"),
    alt: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    
    const updates: any = {};
    if (args.alt !== undefined) updates.alt = args.alt;
    if (args.tags !== undefined) updates.tags = args.tags;
    
    return await ctx.db.patch(args.id, updates);
  },
});

// Delete media file
export const deleteMedia = mutation({
  args: { id: v.id("media") },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    
    const media = await ctx.db.get(args.id);
    if (!media) {
      throw new Error("Media file not found");
    }
    
    // TODO: Check if media is being used in content before deleting
    // This would require scanning all content for references to this media
    
    return await ctx.db.delete(args.id);
  },
});

// Get media usage statistics
export const getMediaStats = query({
  args: {},
  handler: async (ctx) => {
    const allMedia = await ctx.db.query("media").collect();
    
    const stats = {
      total: allMedia.length,
      totalSize: allMedia.reduce((sum, media) => sum + media.size, 0),
      byType: {} as Record<string, number>,
      byUploader: {} as Record<string, number>,
      recentUploads: allMedia
        .sort((a, b) => b.createdAt - a.createdAt)
        .slice(0, 10),
    };
    
    // Group by mime type
    for (const media of allMedia) {
      const type = media.mimeType.split('/')[0];
      stats.byType[type] = (stats.byType[type] || 0) + 1;
    }
    
    // Group by uploader
    for (const media of allMedia) {
      stats.byUploader[media.uploadedBy] = (stats.byUploader[media.uploadedBy] || 0) + 1;
    }
    
    return stats;
  },
});

// Get all unique tags
export const getAllTags = query({
  args: {},
  handler: async (ctx) => {
    const allMedia = await ctx.db.query("media").collect();
    const tagSet = new Set<string>();
    
    for (const media of allMedia) {
      for (const tag of media.tags) {
        tagSet.add(tag);
      }
    }
    
    return Array.from(tagSet).sort();
  },
});

// Bulk tag operation
export const bulkTagMedia = mutation({
  args: {
    mediaIds: v.array(v.id("media")),
    tags: v.array(v.string()),
    operation: v.union(v.literal("add"), v.literal("remove"), v.literal("replace")),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    
    const results = [];
    
    for (const mediaId of args.mediaIds) {
      const media = await ctx.db.get(mediaId);
      if (!media) continue;
      
      let newTags = [...media.tags];
      
      switch (args.operation) {
        case "add":
          newTags = [...new Set([...newTags, ...args.tags])];
          break;
        case "remove":
          newTags = newTags.filter(tag => !args.tags.includes(tag));
          break;
        case "replace":
          newTags = args.tags;
          break;
      }
      
      await ctx.db.patch(mediaId, { tags: newTags });
      results.push({ id: mediaId, status: "updated" });
    }
    
    return results;
  },
});

// Generate optimized image variants
export const generateImageVariants = mutation({
  args: {
    mediaId: v.id("media"),
    variants: v.array(v.object({
      name: v.string(),
      width: v.number(),
      height: v.optional(v.number()),
      quality: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    
    const media = await ctx.db.get(args.mediaId);
    if (!media) {
      throw new Error("Media file not found");
    }
    
    if (!media.mimeType.startsWith("image/")) {
      throw new Error("Media file is not an image");
    }
    
    // In a real implementation, this would:
    // 1. Download the original image
    // 2. Generate optimized variants using a service like Sharp or Cloudinary
    // 3. Upload variants to CDN
    // 4. Store variant URLs in the database
    
    // For now, we'll just simulate the process
    const variants = args.variants.map(variant => ({
      name: variant.name,
      url: `${media.url}?w=${variant.width}&h=${variant.height || 'auto'}&q=${variant.quality || 80}`,
      width: variant.width,
      height: variant.height,
      quality: variant.quality || 80,
    }));
    
    // Store variants in media metadata (you might want a separate table for this)
    await ctx.db.patch(args.mediaId, {
      variants: variants as any,
    });
    
    return variants;
  },
});

// Clean up unused media files
export const cleanupUnusedMedia = mutation({
  args: {
    dryRun: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    
    const dryRun = args.dryRun || false;
    const allMedia = await ctx.db.query("media").collect();
    const allContent = await ctx.db.query("content").collect();
    
    // Find media files that are not referenced in any content
    const usedMediaUrls = new Set<string>();
    
    for (const content of allContent) {
      const contentString = JSON.stringify(content.data);
      for (const media of allMedia) {
        if (contentString.includes(media.url) || contentString.includes(media.filename)) {
          usedMediaUrls.add(media.url);
        }
      }
    }
    
    const unusedMedia = allMedia.filter(media => !usedMediaUrls.has(media.url));
    
    if (!dryRun) {
      // Actually delete unused media
      for (const media of unusedMedia) {
        await ctx.db.delete(media._id);
      }
    }
    
    return {
      total: allMedia.length,
      unused: unusedMedia.length,
      deleted: dryRun ? 0 : unusedMedia.length,
      unusedFiles: unusedMedia.map(media => ({
        id: media._id,
        filename: media.filename,
        size: media.size,
        createdAt: media.createdAt,
      })),
    };
  },
});
