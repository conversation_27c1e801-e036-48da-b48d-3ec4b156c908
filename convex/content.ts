import { v } from "convex/values";
import { mutation, query, internalMutation } from "./_generated/server";

// Helper function to get current user and check permissions
async function getCurrentUser(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Not authenticated");
  }

  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
    .first();

  if (!user) {
    throw new Error("User not found");
  }

  if (!user.isActive) {
    throw new Error("User account is deactivated");
  }

  return user;
}

function hasPermission(user: any, permission: string): boolean {
  const permissions: Record<string, string[]> = {
    viewer: ["content:read", "media:read"],
    content_editor: [
      "content:read", "content:write", "content:publish",
      "media:read", "media:write", "media:upload"
    ],
    admin: [
      "content:read", "content:write", "content:publish", "content:delete",
      "media:read", "media:write", "media:upload", "media:delete",
      "users:read", "users:write", "analytics:read",
      "settings:read", "settings:write"
    ],
    super_admin: ["*"],
  };

  const userPermissions = permissions[user.role] || [];
  return userPermissions.includes("*") || userPermissions.includes(permission);
}

// Get content by identifier and language with caching
export const getContent = query({
  args: {
    identifier: v.string(),
    language: v.optional(v.string()),
    includeDraft: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const includeDraft = args.includeDraft || false;

    // Build status filter
    const statusFilter = includeDraft ?
      (q: any) => q.or(q.eq(q.field("status"), "published"), q.eq(q.field("status"), "draft")) :
      (q: any) => q.eq(q.field("status"), "published");

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", language)
      )
      .filter(statusFilter)
      .order("desc") // Get latest version first
      .first();

    if (!content) {
      // Fallback to English if content not found in requested language
      if (language !== "en") {
        return await ctx.db
          .query("content")
          .withIndex("by_identifier_language", (q) =>
            q.eq("identifier", args.identifier).eq("language", "en")
          )
          .filter(statusFilter)
          .order("desc")
          .first();
      }
      return null;
    }

    // Get content type information
    const contentType = await ctx.db.get(content.contentTypeId);

    return {
      ...content,
      contentType,
    };
  },
});

// Get all content for a specific language with pagination
export const getAllContent = query({
  args: {
    language: v.optional(v.string()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const status = args.status || "published";
    const limit = args.limit || 50;

    let query = ctx.db
      .query("content")
      .withIndex("by_language", (q) => q.eq("language", language))
      .filter((q) => q.eq(q.field("status"), status))
      .order("desc");

    if (args.cursor) {
      // Implement cursor-based pagination if needed
    }

    const results = await query.take(limit);

    // Enrich with content type information
    const enrichedResults = await Promise.all(
      results.map(async (content) => {
        const contentType = await ctx.db.get(content.contentTypeId);
        return {
          ...content,
          contentType,
        };
      })
    );

    return enrichedResults;
  },
});

// Create or update content (admin only)
export const upsertContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    data: v.any(),
    contentTypeId: v.id("contentTypes"),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    // Check permissions based on status
    const status = args.status || "draft";
    const requiredPermission = status === "published" ? "content:publish" : "content:write";
    if (!hasPermission(user, requiredPermission)) {
      throw new Error(`Insufficient permissions: ${requiredPermission} required`);
    }
    
    const existing = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    const now = Date.now();
    
    if (existing) {
      // Create history entry
      await ctx.db.insert("contentHistory", {
        contentId: existing._id,
        data: existing.data,
        version: existing.version,
        changedBy: "system", // TODO: Get from auth
        createdAt: now,
      });

      // Update existing content
      return await ctx.db.patch(existing._id, {
        data: args.data,
        status,
        version: existing.version + 1,
        updatedAt: now,
      });
    } else {
      // Create new content
      return await ctx.db.insert("content", {
        identifier: args.identifier,
        language: args.language,
        data: args.data,
        contentTypeId: args.contentTypeId,
        status,
        version: 1,
        createdBy: "system", // TODO: Get from auth
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Get content by multiple identifiers (batch fetch)
export const getContentBatch = query({
  args: {
    identifiers: v.array(v.string()),
    language: v.optional(v.string()),
    includeDraft: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const includeDraft = args.includeDraft || false;

    const results: Record<string, any> = {};

    for (const identifier of args.identifiers) {
      const statusFilter = includeDraft ?
        (q: any) => q.or(q.eq(q.field("status"), "published"), q.eq(q.field("status"), "draft")) :
        (q: any) => q.eq(q.field("status"), "published");

      const content = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", identifier).eq("language", language)
        )
        .filter(statusFilter)
        .order("desc")
        .first();

      if (content) {
        const contentType = await ctx.db.get(content.contentTypeId);
        results[identifier] = {
          ...content,
          contentType,
        };
      } else if (language !== "en") {
        // Fallback to English
        const fallbackContent = await ctx.db
          .query("content")
          .withIndex("by_identifier_language", (q) =>
            q.eq("identifier", identifier).eq("language", "en")
          )
          .filter(statusFilter)
          .order("desc")
          .first();

        if (fallbackContent) {
          const contentType = await ctx.db.get(fallbackContent.contentTypeId);
          results[identifier] = {
            ...fallbackContent,
            contentType,
          };
        }
      }
    }

    return results;
  },
});

// Get content history with pagination
export const getContentHistory = query({
  args: {
    identifier: v.string(),
    language: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!content) return [];

    const history = await ctx.db
      .query("contentHistory")
      .withIndex("by_content", (q) => q.eq("contentId", content._id))
      .order("desc")
      .take(limit);

    // Enrich with user information if available
    const enrichedHistory = await Promise.all(
      history.map(async (entry) => {
        const user = await ctx.db
          .query("users")
          .filter((q) => q.eq(q.field("clerkId"), entry.changedBy))
          .first();

        return {
          ...entry,
          user: user ? {
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          } : null,
        };
      })
    );

    return enrichedHistory;
  },
});

// Publish content (change from draft to published)
export const publishContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    publishNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!hasPermission(user, "content:publish")) {
      throw new Error("Insufficient permissions: content:publish required");
    }

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .filter((q) => q.eq(q.field("status"), "draft"))
      .first();

    if (!content) {
      throw new Error("Draft content not found");
    }

    // Create history entry
    await ctx.db.insert("contentHistory", {
      contentId: content._id,
      data: content.data,
      version: content.version,
      changedBy: "system", // TODO: Get from auth
      changeNote: args.publishNote || "Published content",
      createdAt: Date.now(),
    });

    // Update content status
    return await ctx.db.patch(content._id, {
      status: "published",
      updatedAt: Date.now(),
    });
  },
});

// Unpublish content (change from published to draft)
export const unpublishContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    unpublishNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .filter((q) => q.eq(q.field("status"), "published"))
      .first();

    if (!content) {
      throw new Error("Published content not found");
    }

    // Create history entry
    await ctx.db.insert("contentHistory", {
      contentId: content._id,
      data: content.data,
      version: content.version,
      changedBy: "system", // TODO: Get from auth
      changeNote: args.unpublishNote || "Unpublished content",
      createdAt: Date.now(),
    });

    // Update content status
    return await ctx.db.patch(content._id, {
      status: "draft",
      updatedAt: Date.now(),
    });
  },
});

// Duplicate content to another language
export const duplicateContent = mutation({
  args: {
    identifier: v.string(),
    fromLanguage: v.string(),
    toLanguage: v.string(),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const sourceContent = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.fromLanguage)
      )
      .first();

    if (!sourceContent) {
      throw new Error("Source content not found");
    }

    // Check if target already exists
    const existingTarget = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.toLanguage)
      )
      .first();

    if (existingTarget) {
      throw new Error("Content already exists in target language");
    }

    const now = Date.now();
    const status = args.status || "draft";

    // Create new content
    return await ctx.db.insert("content", {
      identifier: args.identifier,
      language: args.toLanguage,
      data: sourceContent.data, // Copy data structure
      contentTypeId: sourceContent.contentTypeId,
      status,
      version: 1,
      createdBy: "system", // TODO: Get from auth
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Revert content to a previous version
export const revertContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    targetVersion: v.number(),
    revertNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const currentContent = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!currentContent) {
      throw new Error("Content not found");
    }

    // Find the target version in history
    const targetHistory = await ctx.db
      .query("contentHistory")
      .withIndex("by_version", (q) =>
        q.eq("contentId", currentContent._id).eq("version", args.targetVersion)
      )
      .first();

    if (!targetHistory) {
      throw new Error("Target version not found in history");
    }

    // Create history entry for current state
    await ctx.db.insert("contentHistory", {
      contentId: currentContent._id,
      data: currentContent.data,
      version: currentContent.version,
      changedBy: "system", // TODO: Get from auth
      changeNote: args.revertNote || `Reverted to version ${args.targetVersion}`,
      createdAt: Date.now(),
    });

    // Update content with target version data
    return await ctx.db.patch(currentContent._id, {
      data: targetHistory.data,
      version: currentContent.version + 1,
      updatedAt: Date.now(),
    });
  },
});

// Search content across all languages and types
export const searchContent = query({
  args: {
    query: v.string(),
    language: v.optional(v.string()),
    contentType: v.optional(v.string()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const status = args.status || "published";

    // Get all content matching status
    let query = ctx.db
      .query("content")
      .filter((q) => q.eq(q.field("status"), status));

    if (args.language) {
      query = query.withIndex("by_language", (q) => q.eq("language", args.language));
    }

    const allContent = await query.collect();

    // Filter by content type if specified
    let filteredContent = allContent;
    if (args.contentType) {
      const contentType = await ctx.db
        .query("contentTypes")
        .withIndex("by_name", (q) => q.eq("name", args.contentType))
        .first();

      if (contentType) {
        filteredContent = allContent.filter(c => c.contentTypeId === contentType._id);
      }
    }

    // Simple text search in content data
    const searchResults = filteredContent.filter(content => {
      const searchText = args.query.toLowerCase();
      const contentString = JSON.stringify(content.data).toLowerCase();
      return contentString.includes(searchText) ||
             content.identifier.toLowerCase().includes(searchText);
    });

    // Take only the requested limit
    const limitedResults = searchResults.slice(0, limit);

    // Enrich with content type information
    const enrichedResults = await Promise.all(
      limitedResults.map(async (content) => {
        const contentType = await ctx.db.get(content.contentTypeId);
        return {
          ...content,
          contentType,
        };
      })
    );

    return enrichedResults;
  },
});

// Get content statistics
export const getContentStats = query({
  args: {
    language: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const language = args.language;

    let query = ctx.db.query("content");
    if (language) {
      query = query.withIndex("by_language", (q) => q.eq("language", language));
    }

    const allContent = await query.collect();

    const stats = {
      total: allContent.length,
      published: allContent.filter(c => c.status === "published").length,
      draft: allContent.filter(c => c.status === "draft").length,
      byLanguage: {} as Record<string, number>,
      byContentType: {} as Record<string, number>,
      recentlyUpdated: allContent
        .sort((a, b) => b.updatedAt - a.updatedAt)
        .slice(0, 10),
    };

    // Group by language
    for (const content of allContent) {
      stats.byLanguage[content.language] = (stats.byLanguage[content.language] || 0) + 1;
    }

    // Group by content type
    const contentTypes = await ctx.db.query("contentTypes").collect();
    const contentTypeMap = new Map(contentTypes.map(ct => [ct._id, ct.name]));

    for (const content of allContent) {
      const typeName = contentTypeMap.get(content.contentTypeId) || "unknown";
      stats.byContentType[typeName] = (stats.byContentType[typeName] || 0) + 1;
    }

    return stats;
  },
});

// Get content by ID
export const getContentById = query({
  args: { id: v.id("content") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Add web development service card
export const addWebDevServiceCard = mutation({
  args: {},
  handler: async (ctx) => {
    // Get the service_card content type
    const serviceCardType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "service_card"))
      .first();

    if (!serviceCardType) {
      throw new Error("Service card content type not found");
    }

    const now = Date.now();

    // Create English version
    const enCard = await ctx.db.insert("content", {
      identifier: "service-webdev",
      language: "en",
      contentTypeId: serviceCardType._id,
      status: "published" as const,
      data: {
        title: "Web & Mobile Development",
        description: "Modern, responsive websites and mobile applications built with cutting-edge technologies. Custom development solutions including e-commerce, web apps, and mobile apps with SEO optimization.",
        icon: "Code",
      },
      version: 1,
      createdBy: "system",
      createdAt: now,
      updatedAt: now,
    });

    // Create Spanish version
    const esCard = await ctx.db.insert("content", {
      identifier: "service-webdev",
      language: "es",
      contentTypeId: serviceCardType._id,
      status: "published" as const,
      data: {
        title: "Desarrollo Web y Móvil",
        description: "Sitios web modernos y responsivos y aplicaciones móviles construidas con tecnologías de vanguardia. Soluciones de desarrollo personalizado incluyendo comercio electrónico, aplicaciones web y móviles con optimización SEO.",
        icon: "Code",
      },
      version: 1,
      createdBy: "system",
      createdAt: now,
      updatedAt: now,
    });

    return { enCard, esCard };
  },
});

// Add image to web development service card
export const addWebDevImage = mutation({
  args: {},
  handler: async (ctx) => {
    const imagePath = "/img/Coding_img.jpg";

    // Update English version
    const enContent = await ctx.db
      .query("content")
      .filter((q) => q.and(
        q.eq(q.field("identifier"), "service-webdev"),
        q.eq(q.field("language"), "en")
      ))
      .first();

    if (enContent) {
      await ctx.db.patch(enContent._id, {
        data: {
          ...enContent.data,
          image: { url: imagePath }
        },
        updatedAt: Date.now()
      });
    }

    // Update Spanish version
    const esContent = await ctx.db
      .query("content")
      .filter((q) => q.and(
        q.eq(q.field("identifier"), "service-webdev"),
        q.eq(q.field("language"), "es")
      ))
      .first();

    if (esContent) {
      await ctx.db.patch(esContent._id, {
        data: {
          ...esContent.data,
          image: { url: imagePath }
        },
        updatedAt: Date.now()
      });
    }

    return { success: true, imagePath, updated: [enContent?._id, esContent?._id].filter(Boolean) };
  },
});

// Update content
export const updateContent = mutation({
  args: {
    id: v.id("content"),
    data: v.optional(v.any()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"), v.literal("archived"))),
  },
  handler: async (ctx, args) => {
    // Check authentication and permissions
    const user = await getCurrentUser(ctx);
    if (!user || !hasPermission(user, "content:write")) {
      throw new Error("Not authorized to update content");
    }

    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new Error("Content not found");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.data !== undefined) {
      // Merge the new data with existing data instead of replacing
      updates.data = {
        ...existing.data,
        ...args.data
      };
    }

    if (args.status !== undefined) {
      updates.status = args.status;
    }

    return await ctx.db.patch(args.id, updates);
  },
});



// Bulk operations
export const bulkUpdateContent = mutation({
  args: {
    updates: v.array(v.object({
      identifier: v.string(),
      language: v.string(),
      data: v.optional(v.any()),
      status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    })),
    changeNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const results = [];
    const now = Date.now();

    for (const update of args.updates) {
      const content = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", update.identifier).eq("language", update.language)
        )
        .first();

      if (content) {
        // Create history entry
        await ctx.db.insert("contentHistory", {
          contentId: content._id,
          data: content.data,
          version: content.version,
          changedBy: "system", // TODO: Get from auth
          changeNote: args.changeNote || "Bulk update",
          createdAt: now,
        });

        // Update content
        const updateData: any = {
          version: content.version + 1,
          updatedAt: now,
        };

        if (update.data !== undefined) updateData.data = update.data;
        if (update.status !== undefined) updateData.status = update.status;

        await ctx.db.patch(content._id, updateData);
        results.push({ identifier: update.identifier, language: update.language, status: "updated" });
      } else {
        results.push({ identifier: update.identifier, language: update.language, status: "not_found" });
      }
    }

    return results;
  },
});
