import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Submit a contact form
export const submitContactForm = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    phone: v.optional(v.string()),
    company: v.optional(v.string()),
    service: v.optional(v.string()),
    message: v.string(),
    source: v.optional(v.string()), // Which page/form the submission came from
  },
  handler: async (ctx, args) => {
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(args.email)) {
      throw new Error("Invalid email address");
    }

    // Create the contact form submission
    const contactId = await ctx.db.insert("contactForms", {
      name: args.name,
      email: args.email,
      phone: args.phone,
      company: args.company,
      service: args.service,
      message: args.message,
      source: args.source || "website",
      status: "new",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return contactId;
  },
});

// Get all contact form submissions (admin only)
export const getAllContactForms = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user has admin permissions
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !["super_admin", "admin"].includes(user.role)) {
      throw new Error("Not authorized");
    }

    return await ctx.db
      .query("contactForms")
      .order("desc")
      .collect();
  },
});

// Get contact form by ID (admin only)
export const getContactForm = query({
  args: { id: v.id("contactForms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user has admin permissions
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !["super_admin", "admin"].includes(user.role)) {
      throw new Error("Not authorized");
    }

    return await ctx.db.get(args.id);
  },
});

// Update contact form status (admin only)
export const updateContactFormStatus = mutation({
  args: {
    id: v.id("contactForms"),
    status: v.union(v.literal("new"), v.literal("in_progress"), v.literal("resolved"), v.literal("closed")),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user has admin permissions
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !["super_admin", "admin"].includes(user.role)) {
      throw new Error("Not authorized");
    }

    const existingForm = await ctx.db.get(args.id);
    if (!existingForm) {
      throw new Error("Contact form not found");
    }

    await ctx.db.patch(args.id, {
      status: args.status,
      notes: args.notes,
      updatedAt: Date.now(),
      updatedBy: user._id,
    });

    return args.id;
  },
});

// Get contact form statistics (admin only)
export const getContactFormStats = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user has admin permissions
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !["super_admin", "admin"].includes(user.role)) {
      throw new Error("Not authorized");
    }

    const allForms = await ctx.db.query("contactForms").collect();
    
    const stats = {
      total: allForms.length,
      new: allForms.filter(form => form.status === "new").length,
      inProgress: allForms.filter(form => form.status === "in_progress").length,
      resolved: allForms.filter(form => form.status === "resolved").length,
      closed: allForms.filter(form => form.status === "closed").length,
    };

    // Get submissions by month for the last 6 months
    const sixMonthsAgo = Date.now() - (6 * 30 * 24 * 60 * 60 * 1000);
    const recentForms = allForms.filter(form => form.createdAt > sixMonthsAgo);
    
    const monthlyStats = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = Date.now() - (i * 30 * 24 * 60 * 60 * 1000);
      const monthEnd = Date.now() - ((i - 1) * 30 * 24 * 60 * 60 * 1000);
      const monthForms = recentForms.filter(form => 
        form.createdAt >= monthStart && form.createdAt < monthEnd
      );
      
      const monthName = new Date(monthStart).toLocaleDateString('en-US', { month: 'short' });
      monthlyStats.push({
        month: monthName,
        count: monthForms.length,
      });
    }

    return {
      ...stats,
      monthlyStats,
    };
  },
});

// Delete contact form (admin only)
export const deleteContactForm = mutation({
  args: { id: v.id("contactForms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user has admin permissions
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !["super_admin", "admin"].includes(user.role)) {
      throw new Error("Not authorized");
    }

    await ctx.db.delete(args.id);
    return args.id;
  },
});

// Get recent contact forms (admin only)
export const getRecentContactForms = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user has admin permissions
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || !["super_admin", "admin"].includes(user.role)) {
      throw new Error("Not authorized");
    }

    return await ctx.db
      .query("contactForms")
      .order("desc")
      .take(args.limit || 10);
  },
});
