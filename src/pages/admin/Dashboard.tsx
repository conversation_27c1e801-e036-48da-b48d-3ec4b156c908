import { useAuth } from "@/hooks/useAuth";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  FileText,
  MessageSquare,
  Shield,
  BarChart3
} from "lucide-react";
import { QuickStatsWidget, RealTimeWidget, ActivityFeedWidget } from "@/components/admin/analytics/RealTimeWidget";
import { Link } from "react-router-dom";

export const AdminDashboard = () => {
  const { user } = useAuth();

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return "bg-red-100 text-red-800";
      case "admin":
        return "bg-purple-100 text-purple-800";
      case "content_editor":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {user?.firstName}!
            </h1>
            <p className="text-blue-100">
              Here's what's happening with your website today.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="w-6 h-6" />
            <Badge className={`${getRoleBadgeColor(user?.role || "viewer")} border-white`}>
              {user?.role?.replace("_", " ")}
            </Badge>
          </div>
        </div>
      </div>

      {/* Analytics Stats */}
      <QuickStatsWidget />

      {/* Analytics and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RealTimeWidget />
        <ActivityFeedWidget />

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <FileText className="w-6 h-6 text-blue-600 mb-2" />
                <p className="font-medium text-gray-900">Edit Content</p>
                <p className="text-sm text-gray-600">Update website content</p>
              </button>
              <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <Users className="w-6 h-6 text-green-600 mb-2" />
                <p className="font-medium text-gray-900">Manage Users</p>
                <p className="text-sm text-gray-600">Add or edit users</p>
              </button>
              <button className="p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <MessageSquare className="w-6 h-6 text-purple-600 mb-2" />
                <p className="font-medium text-gray-900">View Contacts</p>
                <p className="text-sm text-gray-600">Check new inquiries</p>
              </button>
              <Link to="/admin/analytics" className="block p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <BarChart3 className="w-6 h-6 text-orange-600 mb-2" />
                <p className="font-medium text-gray-900">Analytics</p>
                <p className="text-sm text-gray-600">View detailed analytics</p>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
