
import Layout from '@/components/Layout';
import { MapPin, Phone, Mail, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { ContactForm } from '@/components/ContactForm';
import { useTranslation } from '@/contexts/I18nContext';
import { DynamicContent } from '@/components/content/DynamicContent';
import { ContentProvider } from '@/components/content/ContentProvider';

const ContactContent = () => {
  const { t } = useTranslation();
  const contactInfo = [
    {
      icon: MapPin,
      title: t('contact.address'),
      details: ['Conakry, Guinea', 'Central Business District']
    },
    {
      icon: Phone,
      title: t('contact.phone'),
      details: ['+224 123 456 789', '+224 987 654 321']
    },
    {
      icon: Mail,
      title: t('contact.email'),
      details: ['<EMAIL>', '<EMAIL>']
    },
    {
      icon: Clock,
      title: t('contact.businessHours'),
      details: ['Mon - Fri: 8:00 AM - 6:00 PM', t('contact.emergencySupport')]
    }
  ];

  return (
    <>
      {/* Dynamic Contact Hero Section */}
      <DynamicContent
        identifier="contact-hero"
        className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20"
        fallback={
          <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                <span className="text-gradient">{t('contact.title')}</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {t('contact.subtitle')}
              </p>
            </div>
          </section>
        }
      />

      {/* Contact Content */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <ContactForm
              source="contact-page"
              title={t('contact.title')}
              subtitle="Fill out the form below and we'll get back to you within 24 hours to discuss your requirements."
            />

            {/* Dynamic Contact Information */}
            <DynamicContent
              identifier="contact-info"
              fallback={
                <div className="space-y-8">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">
                      Get in Touch
                    </h2>
                    <p className="text-gray-600 mb-8">
                      Our team is here to help you with any questions about our services.
                      Reach out through any of the following channels.
                    </p>
                  </div>

                  <div className="space-y-6">
                    {contactInfo.map((item, index) => {
                      const IconComponent = item.icon;
                      return (
                        <Card key={index} className="border-0 shadow-lg">
                          <CardContent className="p-6">
                            <div className="flex items-start space-x-4">
                              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <IconComponent className="w-6 h-6 text-blue-600" />
                              </div>
                              <div>
                                <h3 className="font-bold text-gray-900 mb-2">
                                  {item.title}
                                </h3>
                                {item.details.map((detail, detailIndex) => (
                                  <p key={detailIndex} className="text-gray-600">
                                    {detail}
                                  </p>
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>

                  {/* Map Placeholder */}
                  <Card className="border-0 shadow-lg">
                    <CardContent className="p-0">
                      <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                        <div className="text-center text-gray-500">
                          <MapPin className="w-12 h-12 mx-auto mb-2" />
                          <p>Interactive Map Coming Soon</p>
                          <p className="text-sm">Malabo, Equatorial Guinea</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              }
            />
          </div>
        </div>
      </section>
    </>
  );
};

const Contact = () => {
  return (
    <ContentProvider defaultLanguage="en">
      <Layout>
        <ContactContent />
      </Layout>
    </ContentProvider>
  );
};

export default Contact;
