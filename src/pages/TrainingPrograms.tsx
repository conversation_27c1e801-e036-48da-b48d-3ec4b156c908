import Layout from '@/components/Layout';
import { GraduationCap, Users, Clock, Award, BookOpen, Monitor, Target, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/contexts/I18nContext';

const TrainingPrograms = () => {
  const { t } = useTranslation();

  const programs = [
    {
      title: 'Corporate PC Skills',
      duration: '2 weeks',
      level: 'Beginner',
      participants: '15-20',
      description: 'Essential computer skills for the modern workplace including file management, internet usage, and basic troubleshooting.',
      topics: [
        'Computer basics and navigation',
        'File and folder management',
        'Internet browsing and email',
        'Basic troubleshooting',
        'Digital security awareness'
      ],
      icon: Monitor,
      price: '$150'
    },
    {
      title: 'Microsoft Office Suite',
      duration: '3 weeks',
      level: 'Intermediate',
      participants: '12-15',
      description: 'Comprehensive training on Word, Excel, PowerPoint, and Outlook for professional productivity.',
      topics: [
        'Microsoft Word advanced features',
        'Excel formulas and data analysis',
        'PowerPoint presentation design',
        'Outlook email management',
        'Integration between Office apps'
      ],
      icon: BookOpen,
      price: '$200'
    },
    {
      title: 'Windows Administration',
      duration: '4 weeks',
      level: 'Advanced',
      participants: '8-12',
      description: 'Advanced Windows system administration for IT professionals and power users.',
      topics: [
        'Windows Server management',
        'User and group administration',
        'Network configuration',
        'Security policies',
        'System monitoring and maintenance'
      ],
      icon: Target,
      price: '$300'
    },
    {
      title: 'Leadership Development',
      duration: '2 weeks',
      level: 'All Levels',
      participants: '10-15',
      description: 'Essential leadership skills for managers and team leaders in the digital age.',
      topics: [
        'Leadership fundamentals',
        'Team management',
        'Communication skills',
        'Conflict resolution',
        'Digital leadership tools'
      ],
      icon: Users,
      price: '$180'
    },
    {
      title: 'Time Management',
      duration: '1 week',
      level: 'All Levels',
      participants: '15-25',
      description: 'Effective time management strategies and digital tools for increased productivity.',
      topics: [
        'Time management principles',
        'Priority setting techniques',
        'Digital productivity tools',
        'Work-life balance',
        'Stress management'
      ],
      icon: Clock,
      price: '$100'
    }
  ];

  const benefits = [
    {
      icon: Award,
      title: 'Certified Training',
      description: 'Receive official certificates upon successful completion of programs.'
    },
    {
      icon: Users,
      title: 'Expert Instructors',
      description: 'Learn from experienced professionals with industry expertise.'
    },
    {
      icon: TrendingUp,
      title: 'Career Growth',
      description: 'Enhance your skills and advance your career opportunities.'
    },
    {
      icon: Monitor,
      title: 'Hands-on Learning',
      description: 'Practical exercises and real-world scenarios for better understanding.'
    }
  ];

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
        {/* Hero Section */}
        <section className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="text-gradient">Training Programs</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Professional development and technical training programs designed to enhance 
              your skills and advance your career in the digital age.
            </p>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose Our Training?
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Our comprehensive training programs are designed to provide practical skills 
                and knowledge that you can immediately apply in your work.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <Icon className="w-6 h-6 text-purple-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {benefit.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Programs Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Available Programs
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Choose from our range of professional training programs tailored to different skill levels and career goals.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {programs.map((program, index) => {
                const Icon = program.icon;
                return (
                  <Card key={index} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <Icon className="w-5 h-5 text-purple-600" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-gray-900">
                              {program.title}
                            </h3>
                            <Badge className={getLevelColor(program.level)}>
                              {program.level}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-purple-600">
                            {program.price}
                          </div>
                          <div className="text-sm text-gray-500">per person</div>
                        </div>
                      </div>

                      <p className="text-gray-600 mb-4">
                        {program.description}
                      </p>

                      <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                        <div className="text-center">
                          <Clock className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <div className="font-medium text-gray-900">{program.duration}</div>
                          <div className="text-gray-500">Duration</div>
                        </div>
                        <div className="text-center">
                          <Users className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <div className="font-medium text-gray-900">{program.participants}</div>
                          <div className="text-gray-500">Participants</div>
                        </div>
                        <div className="text-center">
                          <GraduationCap className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <div className="font-medium text-gray-900">Certificate</div>
                          <div className="text-gray-500">Included</div>
                        </div>
                      </div>

                      <div className="mb-6">
                        <h4 className="font-semibold text-gray-900 mb-2">Topics Covered:</h4>
                        <ul className="space-y-1">
                          {program.topics.map((topic, topicIndex) => (
                            <li key={topicIndex} className="text-sm text-gray-600 flex items-center">
                              <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2"></div>
                              {topic}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <Button className="w-full">
                        Enroll Now
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
              <GraduationCap className="w-16 h-16 mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4">
                Ready to Advance Your Skills?
              </h2>
              <p className="text-xl mb-6 opacity-90">
                Join our training programs and take your career to the next level with practical, 
                industry-relevant skills.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary">
                  View Schedule
                </Button>
                <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-purple-600">
                  Contact Us
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
};

export default TrainingPrograms;
