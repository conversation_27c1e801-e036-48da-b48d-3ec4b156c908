
import Layout from '@/components/Layout';
import { Users, Target, Award, Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { DynamicContent } from '@/components/content/DynamicContent';
import { ContentProvider } from '@/components/content/ContentProvider';

const AboutContent = () => {
  const values = [
    {
      icon: Target,
      title: 'Our Mission',
      description: 'To provide cutting-edge technology and security solutions that protect and empower businesses across Equatorial Guinea.'
    },
    {
      icon: Award,
      title: 'Excellence',
      description: 'We maintain the highest standards in everything we do, from initial consultation to ongoing support.'
    },
    {
      icon: Users,
      title: 'Partnership',
      description: 'We work closely with our clients to understand their unique needs and deliver tailored solutions.'
    },
    {
      icon: Clock,
      title: 'Reliability',
      description: 'Our 24/7 support and monitoring ensure your systems are always protected and operational.'
    }
  ];

  const timeline = [
    {
      year: '2009',
      title: 'Company Founded',
      description: 'OfficeTech Guinea was established with a vision to bring advanced technology solutions to the region.'
    },
    {
      year: '2012',
      title: 'First Major Contract',
      description: 'Secured our first major cybersecurity implementation for a leading financial institution.'
    },
    {
      year: '2016',
      title: 'Expansion',
      description: 'Expanded our services to include comprehensive network infrastructure and surveillance systems.'
    },
    {
      year: '2020',
      title: 'Digital Transformation',
      description: 'Helped over 100 businesses transition to digital-first operations during the pandemic.'
    },
    {
      year: '2024',
      title: 'Innovation Leader',
      description: 'Recognized as the leading technology solutions provider in Equatorial Guinea.'
    }
  ];

  return (
    <>
      {/* Dynamic About Hero Section */}
      <DynamicContent
        identifier="about-hero"
        className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20"
        fallback={
          <section className="bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="animate-fade-in">
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    About <span className="text-gradient">OfficeTech</span>
                  </h1>
                  <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                    For over 15 years, we've been at the forefront of technology innovation in Equatorial Guinea,
                    helping businesses secure their digital assets and optimize their operations with cutting-edge solutions.
                  </p>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">500+</div>
                      <div className="text-gray-600">Projects Completed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">98%</div>
                      <div className="text-gray-600">Client Satisfaction</div>
                    </div>
                  </div>
                </div>

                <div className="relative animate-fade-in">
                  <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-8 text-white">
                    <h3 className="text-2xl font-bold mb-4">Why Choose Us?</h3>
                    <ul className="space-y-3">
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        15+ years of industry experience
                      </li>
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        Certified security professionals
                      </li>
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        24/7 monitoring and support
                      </li>
                      <li className="flex items-center">
                        <div className="w-2 h-2 bg-white rounded-full mr-3"></div>
                        Local expertise, global standards
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>
        }
      />

      {/* Dynamic Values Section */}
      <DynamicContent
        identifier="about-values"
        className="py-20 bg-white"
        fallback={
          <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  Our Values
                </h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  The principles that guide everything we do and every solution we deliver.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {values.map((value, index) => {
                  const IconComponent = value.icon;
                  return (
                    <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-shadow">
                      <CardContent className="p-8">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                          <IconComponent className="w-8 h-8 text-blue-600" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-4">
                          {value.title}
                        </h3>
                        <p className="text-gray-600">
                          {value.description}
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          </section>
        }
      />

      {/* Timeline Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600">
              Over 15 years of innovation and growth in technology solutions.
            </p>
          </div>

          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-blue-200 hidden md:block"></div>

            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div key={index} className={`flex items-center ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                } flex-col md:flex-row`}>
                  <div className={`w-full md:w-1/2 ${
                    index % 2 === 0 ? 'md:pr-8 md:text-right' : 'md:pl-8'
                  }`}>
                    <Card className="bg-white shadow-lg hover:shadow-xl transition-shadow">
                      <CardContent className="p-6">
                        <div className="text-2xl font-bold text-blue-600 mb-2">
                          {item.year}
                        </div>
                        <h3 className="text-lg font-bold text-gray-900 mb-2">
                          {item.title}
                        </h3>
                        <p className="text-gray-600">
                          {item.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Timeline dot */}
                  <div className="hidden md:block w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg z-10"></div>
                  
                  <div className="w-full md:w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

const About = () => {
  return (
    <ContentProvider defaultLanguage="en">
      <Layout>
        <AboutContent />
      </Layout>
    </ContentProvider>
  );
};

export default About;
