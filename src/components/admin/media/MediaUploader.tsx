import { useState, useCallback, useRef } from "react";
import { useMediaLibrary, validateFile, formatFileSize, getFileTypeIcon, isImageFile } from "@/hooks/useMedia";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Upload, 
  X, 
  Check, 
  AlertCircle,
  Image as ImageIcon,
  File
} from "lucide-react";
import { toast } from "sonner";

interface UploadFile {
  file: File;
  id: string;
  status: "pending" | "uploading" | "success" | "error";
  progress: number;
  error?: string;
  preview?: string;
  alt?: string;
  tags: string[];
}

interface MediaUploaderProps {
  onUploadComplete?: () => void;
  maxFiles?: number;
  acceptedTypes?: string[];
}

export const MediaUploader = ({
  onUploadComplete,
  maxFiles = 10,
  acceptedTypes = ["image/*", "video/*", ".pdf"]
}: MediaUploaderProps) => {
  const { uploadFile } = useMediaLibrary();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFiles = useCallback((files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    const newFiles: UploadFile[] = fileArray.map(file => {
      const validation = validateFile(file);

      return {
        file,
        id: `${Date.now()}-${Math.random()}`,
        status: validation.isValid ? "pending" : "error",
        progress: 0,
        error: validation.error,
        tags: [],
      };
    });

    // Generate previews for images
    newFiles.forEach(uploadFile => {
      if (isImageFile(uploadFile.file.type) && uploadFile.status === "pending") {
        const reader = new FileReader();
        reader.onload = (e) => {
          setUploadFiles(prev =>
            prev.map(f =>
              f.id === uploadFile.id
                ? { ...f, preview: e.target?.result as string }
                : f
            )
          );
        };
        reader.readAsDataURL(uploadFile.file);
      }
    });

    setUploadFiles(prev => [...prev, ...newFiles].slice(0, maxFiles));
  }, [maxFiles]);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    handleFiles(e.dataTransfer.files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  };

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== id));
  };

  const updateFileMetadata = (id: string, updates: Partial<UploadFile>) => {
    setUploadFiles(prev => 
      prev.map(f => f.id === id ? { ...f, ...updates } : f)
    );
  };

  const uploadAllFiles = async () => {
    const validFiles = uploadFiles.filter(f => f.status === "pending");
    if (validFiles.length === 0) return;

    setIsUploading(true);

    for (const uploadFile of validFiles) {
      try {
        // Update status to uploading
        updateFileMetadata(uploadFile.id, { status: "uploading", progress: 0 });

        // Simulate upload progress
        for (let progress = 0; progress <= 100; progress += 20) {
          updateFileMetadata(uploadFile.id, { progress });
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Actually upload the file
        await uploadFile(uploadFile.file, {
          alt: uploadFile.alt,
          tags: uploadFile.tags,
        });

        updateFileMetadata(uploadFile.id, { status: "success", progress: 100 });
      } catch (error) {
        updateFileMetadata(uploadFile.id, { 
          status: "error", 
          error: error instanceof Error ? error.message : "Upload failed" 
        });
      }
    }

    setIsUploading(false);
    
    const successCount = uploadFiles.filter(f => f.status === "success").length;
    if (successCount > 0) {
      toast.success(`Successfully uploaded ${successCount} file${successCount !== 1 ? 's' : ''}`);
      onUploadComplete?.();
    }
  };

  const clearCompleted = () => {
    setUploadFiles(prev => prev.filter(f => f.status === "pending" || f.status === "uploading"));
  };

  const pendingFiles = uploadFiles.filter(f => f.status === "pending");
  const completedFiles = uploadFiles.filter(f => f.status === "success" || f.status === "error");

  return (
    <div className="space-y-6">
      {/* Drop Zone */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? "border-blue-500 bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
        } ${isUploading ? "pointer-events-none opacity-50" : ""}`}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(",")}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={isUploading}
        />
        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        {isDragActive ? (
          <p className="text-lg text-blue-600">Drop files here...</p>
        ) : (
          <div>
            <p className="text-lg text-gray-600 mb-2">
              Drag & drop files here, or click to select
            </p>
            <p className="text-sm text-gray-500">
              Supports images, videos, and documents (max {maxFiles} files)
            </p>
          </div>
        )}
      </div>

      {/* File List */}
      {uploadFiles.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Files ({uploadFiles.length})
            </h3>
            <div className="flex space-x-2">
              {completedFiles.length > 0 && (
                <Button variant="outline" size="sm" onClick={clearCompleted}>
                  Clear Completed
                </Button>
              )}
              {pendingFiles.length > 0 && (
                <Button 
                  onClick={uploadAllFiles} 
                  disabled={isUploading}
                  size="sm"
                >
                  {isUploading ? "Uploading..." : `Upload ${pendingFiles.length} Files`}
                </Button>
              )}
            </div>
          </div>

          <div className="space-y-3">
            {uploadFiles.map((uploadFile) => (
              <FileUploadCard
                key={uploadFile.id}
                uploadFile={uploadFile}
                onRemove={() => removeFile(uploadFile.id)}
                onUpdate={(updates) => updateFileMetadata(uploadFile.id, updates)}
                disabled={isUploading}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Individual file upload card
const FileUploadCard = ({ 
  uploadFile, 
  onRemove, 
  onUpdate, 
  disabled 
}: {
  uploadFile: UploadFile;
  onRemove: () => void;
  onUpdate: (updates: Partial<UploadFile>) => void;
  disabled: boolean;
}) => {
  const getStatusIcon = () => {
    switch (uploadFile.status) {
      case "success":
        return <Check className="w-5 h-5 text-green-600" />;
      case "error":
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case "uploading":
        return <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      default:
        return isImageFile(uploadFile.file.type) ? 
          <ImageIcon className="w-5 h-5 text-gray-400" /> : 
          <File className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (uploadFile.status) {
      case "success":
        return "border-green-200 bg-green-50";
      case "error":
        return "border-red-200 bg-red-50";
      case "uploading":
        return "border-blue-200 bg-blue-50";
      default:
        return "border-gray-200";
    }
  };

  return (
    <Card className={`${getStatusColor()} transition-colors`}>
      <CardContent className="p-4">
        <div className="flex items-start space-x-4">
          {/* Preview/Icon */}
          <div className="flex-shrink-0">
            {uploadFile.preview ? (
              <img 
                src={uploadFile.preview} 
                alt={uploadFile.file.name}
                className="w-16 h-16 object-cover rounded"
              />
            ) : (
              <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center text-2xl">
                {getFileTypeIcon(uploadFile.file.type)}
              </div>
            )}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <div>
                <p className="font-medium truncate" title={uploadFile.file.name}>
                  {uploadFile.file.name}
                </p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(uploadFile.file.size)} • {uploadFile.file.type}
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                {getStatusIcon()}
                {uploadFile.status === "pending" && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onRemove}
                    disabled={disabled}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* Progress Bar */}
            {uploadFile.status === "uploading" && (
              <Progress value={uploadFile.progress} className="mb-2" />
            )}

            {/* Error Message */}
            {uploadFile.status === "error" && uploadFile.error && (
              <p className="text-sm text-red-600 mb-2">{uploadFile.error}</p>
            )}

            {/* Metadata Fields */}
            {uploadFile.status === "pending" && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                <div>
                  <Label htmlFor={`alt-${uploadFile.id}`} className="text-xs">
                    Alt Text
                  </Label>
                  <Input
                    id={`alt-${uploadFile.id}`}
                    value={uploadFile.alt || ""}
                    onChange={(e) => onUpdate({ alt: e.target.value })}
                    placeholder="Describe this image..."
                    disabled={disabled}
                    size="sm"
                  />
                </div>
                
                <div>
                  <Label htmlFor={`tags-${uploadFile.id}`} className="text-xs">
                    Tags (comma separated)
                  </Label>
                  <Input
                    id={`tags-${uploadFile.id}`}
                    value={uploadFile.tags.join(", ")}
                    onChange={(e) => onUpdate({ 
                      tags: e.target.value.split(",").map(tag => tag.trim()).filter(Boolean)
                    })}
                    placeholder="tag1, tag2, tag3"
                    disabled={disabled}
                    size="sm"
                  />
                </div>
              </div>
            )}

            {/* Tags Display */}
            {uploadFile.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {uploadFile.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
