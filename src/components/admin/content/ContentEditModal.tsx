import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, Save, X, Eye, Globe } from "lucide-react";
import { toast } from "sonner";
import { RichTextEditor } from "./RichTextEditor";
import { ImageUploader } from "../media/ImageUploader";

interface ContentEditModalProps {
  contentId: Id<"content"> | null;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (content: any) => void;
}

export const ContentEditModal = ({ 
  contentId, 
  isOpen, 
  onClose, 
  onSave 
}: ContentEditModalProps) => {
  const [formData, setFormData] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'content' | 'settings'>('content');

  // Queries
  const content = useQuery(
    api.content.getContentById,
    contentId ? { id: contentId } : "skip"
  );
  const contentType = useQuery(
    api.contentTypes.getContentTypeById,
    content?.contentTypeId ? { id: content.contentTypeId } : "skip"
  );

  // Mutations
  const updateContent = useMutation(api.content.updateContent);
  const publishContent = useMutation(api.content.publishContent);

  // Initialize form data when content loads
  useEffect(() => {
    if (content) {
      setFormData({
        identifier: content.identifier,
        language: content.language,
        status: content.status,
        data: content.data || {},
      });
    }
  }, [content]);

  // Handle form field changes
  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      data: {
        ...prev.data,
        [fieldName]: value,
      },
    }));
  };

  // Handle metadata changes
  const handleMetaChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Save content
  const handleSave = async (shouldPublish = false) => {
    if (!contentId || !content) return;

    setIsLoading(true);
    try {
      await updateContent({
        id: contentId,
        data: formData.data,
        status: shouldPublish ? "published" : formData.status,
      });

      toast.success(
        shouldPublish ? "Content published successfully!" : "Content saved successfully!"
      );
      
      onSave?.(formData);
      onClose();
    } catch (error) {
      toast.error("Failed to save content");
      console.error("Save error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Render field based on type
  const renderField = (field: any) => {
    const value = formData.data?.[field.name] || field.defaultValue || "";

    switch (field.type) {
      case "text":
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "richText":
        return (
          <RichTextEditor
            value={value}
            onChange={(content) => handleFieldChange(field.name, content)}
            placeholder={field.placeholder}
          />
        );

      case "textarea":
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            rows={4}
          />
        );

      case "number":
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.name, Number(e.target.value))}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "email":
        return (
          <Input
            type="email"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "url":
        return (
          <Input
            type="url"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "image":
        return (
          <ImageUploader
            value={value}
            onChange={(imageData) => handleFieldChange(field.name, imageData)}
            accept="image/*"
          />
        );

      case "color":
        return (
          <Input
            type="color"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            required={field.required}
          />
        );

      default:
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );
    }
  };

  if (!content || !contentType) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-bold">
                Edit {contentType.label}
              </DialogTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={content.status === "published" ? "default" : "secondary"}>
                  {content.status}
                </Badge>
                <span className="text-sm text-gray-500">
                  {content.identifier} • {content.language}
                </span>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setActiveTab(activeTab === 'content' ? 'settings' : 'content')}
              >
                {activeTab === 'content' ? 'Settings' : 'Content'}
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto py-4">
          {activeTab === 'content' ? (
            <div className="space-y-6">
              {contentType.fields?.map((field: any) => (
                <div key={field.name} className="space-y-2">
                  <Label htmlFor={field.name} className="text-sm font-medium">
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  {renderField(field)}
                  {field.description && (
                    <p className="text-xs text-gray-500">{field.description}</p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="identifier">Content Identifier</Label>
                <Input
                  id="identifier"
                  value={formData.identifier || ""}
                  onChange={(e) => handleMetaChange("identifier", e.target.value)}
                  placeholder="unique-identifier"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Language</Label>
                <Select
                  value={formData.language || "en"}
                  onValueChange={(value) => handleMetaChange("language", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status || "draft"}
                  onValueChange={(value) => handleMetaChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          </div>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => handleSave(false)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Save Draft
            </Button>
            
            <Button
              onClick={() => handleSave(true)}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Globe className="w-4 h-4 mr-2" />
              )}
              Publish
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
