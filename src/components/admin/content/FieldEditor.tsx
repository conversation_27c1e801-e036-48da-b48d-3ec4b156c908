import { useState } from "react";
import { FieldDefinition } from "@/types/content";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Plus, X, Upload, Image as ImageIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface FieldEditorProps {
  field: FieldDefinition;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
}

export const FieldEditor = ({ field, value, onChange, error, disabled }: FieldEditorProps) => {
  const [date, setDate] = useState<Date | undefined>(
    field.type === "date" && value ? new Date(value) : undefined
  );

  const handleDateChange = (newDate: Date | undefined) => {
    setDate(newDate);
    onChange(newDate ? newDate.toISOString() : null);
  };

  const renderField = () => {
    switch (field.type) {
      case "text":
        return (
          <Input
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case "richText":
        return (
          <RichTextEditor
            value={value || ""}
            onChange={onChange}
            placeholder={field.placeholder}
            disabled={disabled}
            error={!!error}
          />
        );

      case "number":
        return (
          <Input
            type="number"
            value={value || ""}
            onChange={(e) => onChange(Number(e.target.value))}
            placeholder={field.placeholder}
            min={field.validation?.min}
            max={field.validation?.max}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case "boolean":
        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={value || false}
              onCheckedChange={onChange}
              disabled={disabled}
            />
            <Label className="text-sm text-gray-600">
              {value ? "Enabled" : "Disabled"}
            </Label>
          </div>
        );

      case "select":
        return (
          <Select value={value || ""} onValueChange={onChange} disabled={disabled}>
            <SelectTrigger className={error ? "border-red-500" : ""}>
              <SelectValue placeholder={field.placeholder || "Select an option"} />
            </SelectTrigger>
            <SelectContent>
              {field.validation?.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "multiSelect":
        return (
          <MultiSelectEditor
            options={field.validation?.options || []}
            value={value || []}
            onChange={onChange}
            placeholder={field.placeholder}
            disabled={disabled}
            error={!!error}
          />
        );

      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !date && "text-muted-foreground",
                  error && "border-red-500"
                )}
                disabled={disabled}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={date}
                onSelect={handleDateChange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case "email":
        return (
          <Input
            type="email"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || "Enter email address"}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case "url":
        return (
          <Input
            type="url"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || "https://example.com"}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case "color":
        return (
          <div className="flex items-center space-x-2">
            <Input
              type="color"
              value={value || "#000000"}
              onChange={(e) => onChange(e.target.value)}
              disabled={disabled}
              className="w-16 h-10 p-1 border rounded"
            />
            <Input
              type="text"
              value={value || ""}
              onChange={(e) => onChange(e.target.value)}
              placeholder="#000000"
              disabled={disabled}
              className={cn("flex-1", error && "border-red-500")}
            />
          </div>
        );

      case "image":
        return (
          <ImageUploader
            value={value}
            onChange={onChange}
            disabled={disabled}
            error={!!error}
          />
        );

      case "array":
        return (
          <ArrayEditor
            value={value || []}
            onChange={onChange}
            disabled={disabled}
            error={!!error}
          />
        );

      case "object":
        return (
          <ObjectEditor
            value={value || {}}
            onChange={onChange}
            disabled={disabled}
            error={!!error}
          />
        );

      default:
        return (
          <div className="p-4 border border-dashed border-gray-300 rounded-lg text-center text-gray-500">
            Unsupported field type: {field.type}
          </div>
        );
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label htmlFor={field.name} className="text-sm font-medium">
          {field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        {field.description && (
          <span className="text-xs text-gray-500">{field.description}</span>
        )}
      </div>
      
      {renderField()}
      
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};

// Rich Text Editor Component
const RichTextEditor = ({ value, onChange, placeholder, disabled, error }: {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error: boolean;
}) => {
  // For now, using a textarea. In a real app, you'd use a rich text editor like TinyMCE or Quill
  return (
    <Textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={cn("min-h-[100px]", error && "border-red-500")}
      rows={4}
    />
  );
};

// Multi Select Editor Component
const MultiSelectEditor = ({ options, value, onChange, placeholder, disabled, error }: {
  options: string[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  error: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOption = (option: string) => {
    const newValue = value.includes(option)
      ? value.filter(v => v !== option)
      : [...value, option];
    onChange(newValue);
  };

  return (
    <div className="space-y-2">
      <div className={cn("border rounded-md p-2 min-h-[40px]", error && "border-red-500")}>
        <div className="flex flex-wrap gap-1">
          {value.map((item) => (
            <Badge key={item} variant="secondary" className="flex items-center gap-1">
              {item}
              <X 
                className="w-3 h-3 cursor-pointer" 
                onClick={() => toggleOption(item)}
              />
            </Badge>
          ))}
          {value.length === 0 && (
            <span className="text-gray-500 text-sm">{placeholder || "Select options"}</span>
          )}
        </div>
      </div>
      
      <Select onValueChange={toggleOption} disabled={disabled}>
        <SelectTrigger>
          <SelectValue placeholder="Add option" />
        </SelectTrigger>
        <SelectContent>
          {options.filter(option => !value.includes(option)).map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

// Image Uploader Component
const ImageUploader = ({ value, onChange, disabled, error }: {
  value: any;
  onChange: (value: any) => void;
  disabled?: boolean;
  error: boolean;
}) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, you'd upload to a service like Cloudinary or AWS S3
      const reader = new FileReader();
      reader.onload = (e) => {
        onChange({
          url: e.target?.result as string,
          name: file.name,
          size: file.size,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className={cn("border-2 border-dashed rounded-lg p-4", error && "border-red-500")}>
      {value?.url ? (
        <div className="space-y-2">
          <img src={value.url} alt={value.name} className="max-w-full h-32 object-cover rounded" />
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">{value.name}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onChange(null)}
              disabled={disabled}
            >
              Remove
            </Button>
          </div>
        </div>
      ) : (
        <div className="text-center">
          <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Upload an image</p>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              disabled={disabled}
              className="hidden"
              id="image-upload"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => document.getElementById('image-upload')?.click()}
              disabled={disabled}
            >
              <Upload className="w-4 h-4 mr-2" />
              Choose File
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

// Array Editor Component
const ArrayEditor = ({ value, onChange, disabled, error }: {
  value: any[];
  onChange: (value: any[]) => void;
  disabled?: boolean;
  error: boolean;
}) => {
  const addItem = () => {
    onChange([...value, ""]);
  };

  const updateItem = (index: number, newValue: string) => {
    const newArray = [...value];
    newArray[index] = newValue;
    onChange(newArray);
  };

  const removeItem = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-2">
      {value.map((item, index) => (
        <div key={index} className="flex items-center space-x-2">
          <Input
            value={item}
            onChange={(e) => updateItem(index, e.target.value)}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => removeItem(index)}
            disabled={disabled}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      ))}
      <Button
        variant="outline"
        size="sm"
        onClick={addItem}
        disabled={disabled}
      >
        <Plus className="w-4 h-4 mr-2" />
        Add Item
      </Button>
    </div>
  );
};

// Object Editor Component
const ObjectEditor = ({ value, onChange, disabled, error }: {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  disabled?: boolean;
  error: boolean;
}) => {
  return (
    <div className={cn("border rounded-md p-3", error && "border-red-500")}>
      <Textarea
        value={JSON.stringify(value, null, 2)}
        onChange={(e) => {
          try {
            const parsed = JSON.parse(e.target.value);
            onChange(parsed);
          } catch {
            // Invalid JSON, don't update
          }
        }}
        disabled={disabled}
        className="font-mono text-sm"
        rows={6}
        placeholder="{}"
      />
      <p className="text-xs text-gray-500 mt-1">
        Enter valid JSON object
      </p>
    </div>
  );
};
