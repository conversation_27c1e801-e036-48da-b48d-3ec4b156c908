import { useState, ReactNode } from "react";
import { Link, useLocation, Outlet } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserButton } from "@clerk/clerk-react";
import { 
  Menu,
  Home,
  FileText,
  Image,
  Users,
  Settings,
  BarChart3,
  Globe,
  Shield,
  ChevronDown,
  Bell,
  Search,
  HelpCircle
} from "lucide-react";

interface AdminLayoutProps {
  children?: ReactNode;
}

export const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { user, canEditContent, canManageUsers, canViewAnalytics, canManageSettings } = useAuth();
  const location = useLocation();

  const navigationItems = [
    {
      title: "Dashboard",
      href: "/admin",
      icon: Home,
      permission: true,
    },
    {
      title: "Content",
      icon: FileText,
      permission: canEditContent,
      children: [
        { title: "All Content", href: "/admin/content" },
        { title: "Content Types", href: "/admin/content-types" },
        { title: "Drafts", href: "/admin/content/drafts" },
      ],
    },
    {
      title: "Media Library",
      href: "/admin/media",
      icon: Image,
      permission: canEditContent,
    },
    {
      title: "Users",
      href: "/admin/users",
      icon: Users,
      permission: canManageUsers,
    },
    {
      title: "Analytics",
      href: "/admin/analytics",
      icon: BarChart3,
      permission: canViewAnalytics,
    },
    {
      title: "Translations",
      href: "/admin/translations",
      icon: Globe,
      permission: canEditContent,
    },
    {
      title: "Settings",
      icon: Settings,
      permission: canManageSettings,
      children: [
        { title: "General", href: "/admin/settings/general" },
        { title: "SEO", href: "/admin/settings/seo" },
        { title: "Integrations", href: "/admin/settings/integrations" },
      ],
    },
    {
      title: "Setup",
      href: "/admin/setup",
      icon: Settings,
      permission: canManageSettings,
    },
  ].filter(item => item.permission);

  const isActiveRoute = (href: string) => {
    if (href === "/admin") {
      return location.pathname === "/admin";
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Sidebar */}
      <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden fixed top-4 left-4 z-50 bg-white shadow-md"
          >
            <Menu className="w-5 h-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <Sidebar 
            navigationItems={navigationItems}
            isActiveRoute={isActiveRoute}
            onItemClick={() => setIsSidebarOpen(false)}
          />
        </SheetContent>
      </Sheet>

      {/* Desktop Layout */}
      <div className="flex">
        {/* Desktop Sidebar */}
        <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
          <Sidebar 
            navigationItems={navigationItems}
            isActiveRoute={isActiveRoute}
          />
        </div>

        {/* Main Content */}
        <div className="md:pl-64 flex flex-col flex-1 min-h-screen">
          {/* Top Navigation */}
          <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
            <div className="px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                {/* Left side - breadcrumbs could go here */}
                <div className="flex items-center space-x-4">
                  <h1 className="text-xl font-semibold text-gray-900 md:hidden">
                    Admin
                  </h1>
                </div>

                {/* Right side - user menu and actions */}
                <div className="flex items-center space-x-4">
                  {/* Search */}
                  <Button variant="ghost" size="sm">
                    <Search className="w-4 h-4" />
                  </Button>

                  {/* Notifications */}
                  <Button variant="ghost" size="sm" className="relative">
                    <Bell className="w-4 h-4" />
                    <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                  </Button>

                  {/* Help */}
                  <Button variant="ghost" size="sm">
                    <HelpCircle className="w-4 h-4" />
                  </Button>

                  {/* User Menu */}
                  <div className="flex items-center space-x-3">
                    <div className="hidden sm:block text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {user?.firstName} {user?.lastName}
                      </p>
                      <p className="text-xs text-gray-500 capitalize">
                        {user?.role?.replace('_', ' ')}
                      </p>
                    </div>
                    <UserButton 
                      appearance={{
                        elements: {
                          avatarBox: "w-8 h-8",
                        },
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </header>

          {/* Page Content */}
          <main className="flex-1 bg-gray-50">
            <div className="px-4 sm:px-6 lg:px-8 py-8 pt-16 md:pt-8">
              {children || <Outlet />}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

// Sidebar Component
const Sidebar = ({ 
  navigationItems, 
  isActiveRoute, 
  onItemClick 
}: {
  navigationItems: any[];
  isActiveRoute: (href: string) => boolean;
  onItemClick?: () => void;
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(title)) {
        newSet.delete(title);
      } else {
        newSet.add(title);
      }
      return newSet;
    });
  };

  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex items-center h-16 px-6 border-b border-gray-200">
        <Link to="/admin" className="flex items-center space-x-2">
          <Shield className="w-8 h-8 text-blue-600" />
          <span className="text-xl font-bold text-gray-900">Admin</span>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        {navigationItems.map((item) => (
          <div key={item.title}>
            {item.children ? (
              // Expandable menu item
              <div>
                <button
                  onClick={() => toggleExpanded(item.title)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900"
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="w-5 h-5" />
                    <span>{item.title}</span>
                  </div>
                  <ChevronDown 
                    className={`w-4 h-4 transition-transform ${
                      expandedItems.has(item.title) ? 'rotate-180' : ''
                    }`} 
                  />
                </button>
                
                {expandedItems.has(item.title) && (
                  <div className="ml-8 mt-2 space-y-1">
                    {item.children.map((child: any) => (
                      <Link
                        key={child.href}
                        to={child.href}
                        onClick={onItemClick}
                        className={`block px-3 py-2 text-sm rounded-md transition-colors ${
                          isActiveRoute(child.href)
                            ? 'bg-blue-100 text-blue-700 font-medium'
                            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                        }`}
                      >
                        {child.title}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              // Simple menu item
              <Link
                to={item.href}
                onClick={onItemClick}
                className={`flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  isActiveRoute(item.href)
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <item.icon className="w-5 h-5" />
                <span>{item.title}</span>
              </Link>
            )}
          </div>
        ))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Shield className="w-4 h-4 text-blue-600" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              OfficeTech Admin
            </p>
            <p className="text-xs text-gray-500">
              v1.0.0
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
