import { ReactNode } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { SignIn } from "@clerk/clerk-react";
import { useAuth, UserRole } from "@/hooks/useAuth";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: UserRole;
  requiredPermission?: string;
  fallbackPath?: string;
}

export const ProtectedRoute = ({ 
  children, 
  requiredRole, 
  requiredPermission,
  fallbackPath = "/" 
}: ProtectedRouteProps) => {
  const { isLoaded, isSignedIn, user, hasRole, hasPermission } = useAuth();
  const location = useLocation();

  // Show loading while checking authentication
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect to sign in if not authenticated
  if (!isSignedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <SignIn 
              redirectUrl={location.pathname}
              appearance={{
                elements: {
                  formButtonPrimary: "bg-blue-600 hover:bg-blue-700",
                  card: "shadow-none border-0",
                }
              }}
            />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Wait for user data to load
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Check if user is active
  if (!user.isActive) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Account Deactivated
            </h2>
            <p className="text-gray-600">
              Your account has been deactivated. Please contact an administrator for assistance.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check role requirements
  if (requiredRole && !hasRole(requiredRole)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Access Denied
            </h2>
            <p className="text-gray-600 mb-4">
              You don't have the required permissions to access this page.
            </p>
            <p className="text-sm text-gray-500">
              Required role: {requiredRole}
            </p>
            <p className="text-sm text-gray-500">
              Your role: {user.role}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check permission requirements
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Access Denied
            </h2>
            <p className="text-gray-600 mb-4">
              You don't have the required permissions to access this page.
            </p>
            <p className="text-sm text-gray-500">
              Required permission: {requiredPermission}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

// Convenience components for common protection patterns
export const AdminRoute = ({ children, fallbackPath }: { children: ReactNode; fallbackPath?: string }) => (
  <ProtectedRoute requiredRole="admin" fallbackPath={fallbackPath}>
    {children}
  </ProtectedRoute>
);

export const ContentEditorRoute = ({ children, fallbackPath }: { children: ReactNode; fallbackPath?: string }) => (
  <ProtectedRoute requiredRole="content_editor" fallbackPath={fallbackPath}>
    {children}
  </ProtectedRoute>
);

export const SuperAdminRoute = ({ children, fallbackPath }: { children: ReactNode; fallbackPath?: string }) => (
  <ProtectedRoute requiredRole="super_admin" fallbackPath={fallbackPath}>
    {children}
  </ProtectedRoute>
);

// Permission-based protection
export const PermissionGate = ({
  permission,
  children,
  fallback
}: {
  permission: string;
  children: ReactNode;
  fallback?: ReactNode;
}) => {
  const { hasPermission } = useAuth();

  if (!hasPermission(permission)) {
    return fallback ? <>{fallback}</> : null;
  }

  return <>{children}</>;
};

// Inline permission check component
export const CanAccess = ({
  permission,
  role,
  children,
  fallback
}: {
  permission?: string;
  role?: UserRole;
  children: ReactNode;
  fallback?: ReactNode;
}) => {
  const { hasRole, hasPermission } = useAuth();

  const hasAccess = (role && hasRole(role)) || (permission && hasPermission(permission));

  if (!hasAccess) {
    return fallback ? <>{fallback}</> : null;
  }

  return <>{children}</>;
};
