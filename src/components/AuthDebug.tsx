import { useAuth } from "@clerk/clerk-react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export const AuthDebug = () => {
  const { isLoaded, isSignedIn, user } = useAuth();
  const testAuth = useQuery(api.users.testAuth);

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="font-bold mb-2">Authentication Debug</h3>
      <div className="space-y-2 text-sm">
        <div>
          <strong>Clerk Auth:</strong>
          <ul className="ml-4">
            <li>Loaded: {isLoaded ? "✅" : "❌"}</li>
            <li>Signed In: {isSignedIn ? "✅" : "❌"}</li>
            <li>User ID: {user?.id || "None"}</li>
            <li>Email: {user?.emailAddresses?.[0]?.emailAddress || "None"}</li>
          </ul>
        </div>
        <div>
          <strong>Convex Auth:</strong>
          <ul className="ml-4">
            <li>Has Identity: {testAuth?.hasIdentity ? "✅" : "❌"}</li>
            <li>Subject: {testAuth?.identity?.subject || "None"}</li>
            <li>Email: {testAuth?.identity?.email || "None"}</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
