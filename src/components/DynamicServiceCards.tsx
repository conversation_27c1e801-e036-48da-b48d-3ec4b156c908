import { useContentBatch } from "@/hooks/useContent";
import { DynamicContent } from "./content/DynamicContent";
import { Skeleton } from "@/components/ui/skeleton";

interface DynamicServiceCardsProps {
  language?: string;
  variant?: "homepage" | "services";
  className?: string;
  title?: string;
  subtitle?: string;
  showTitle?: boolean;
}

export const DynamicServiceCards = ({
  language = "en",
  variant = "homepage",
  className = "",
  title,
  subtitle,
  showTitle = true
}: DynamicServiceCardsProps) => {
  const serviceIdentifiers = [
    "service-cybersecurity",
    "service-network", 
    "service-surveillance",
    "service-training",
    "service-webdev"
  ];

  const { content: contentMap, isLoading } = useContentBatch(serviceIdentifiers, language);

  // Loading state
  if (isLoading) {
    return (
      <section className={`py-16 ${variant === "homepage" ? "bg-gray-50" : ""} ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {showTitle && (
            <div className="text-center mb-12">
              <Skeleton className="h-10 w-64 mx-auto mb-4" />
              <Skeleton className="h-6 w-96 mx-auto" />
            </div>
          )}
          <div className={getGridClasses(variant)}>
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-lg p-6 min-h-[280px]">
                <div className="flex items-start mb-4">
                  <Skeleton className="w-16 h-16 rounded-lg mr-4" />
                  <div className="flex-1">
                    <Skeleton className="h-6 w-32 mb-2" />
                  </div>
                </div>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  // No content state
  if (!contentMap || Object.keys(contentMap).length === 0) {
    return null;
  }

  // Get default titles based on variant
  const defaultTitle = variant === "homepage" 
    ? "Our Services" 
    : "Our Main Services";
  
  const defaultSubtitle = variant === "homepage"
    ? "Comprehensive technology solutions designed to secure, connect, and optimize your business operations."
    : "Comprehensive technology solutions organized into key service areas";

  return (
    <section className={`py-16 ${variant === "homepage" ? "bg-gray-50" : ""} ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {showTitle && (
          <div className="text-center mb-12 animate-fade-in-up">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {variant === "homepage" ? (
                <>
                  {title || "Our"} <span className="text-gradient">Services</span>
                </>
              ) : (
                title || defaultTitle
              )}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {subtitle || defaultSubtitle}
            </p>
          </div>
        )}

        <div className={getGridClasses(variant)}>
          {Object.values(contentMap).map((content: any) => (
            <DynamicContent
              key={content.identifier}
              identifier={content.identifier}
              language={language}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

// Helper function to get grid classes based on variant
function getGridClasses(variant: "homepage" | "services"): string {
  if (variant === "homepage") {
    // Homepage: 3 columns on large screens, wider cards with better spacing
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-stretch";
  } else {
    // Services page: 2-3 columns, cards stretch to fill available space
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 justify-items-stretch";
  }
}

export default DynamicServiceCards;
