import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useContentSubscription, useContent } from "@/hooks/useContent";
import { ContentEditModal } from "@/components/admin/content/ContentEditModal";
import { ContentPreviewModal } from "@/components/admin/content/ContentPreviewModal";
import { useAuth } from "@/hooks/useAuth";

interface ContentContextType {
  language: string;
  setLanguage: (language: string) => void;
  isEditMode: boolean;
  setEditMode: (editMode: boolean) => void;
  contentCache: Record<string, any>;
  refreshContent: (identifiers?: string[]) => void;
}

const ContentContext = createContext<ContentContextType | null>(null);

interface ContentProviderProps {
  children: ReactNode;
  defaultLanguage?: string;
  enableEditMode?: boolean;
}

export const ContentProvider = ({
  children,
  defaultLanguage = "en",
  enableEditMode = false
}: ContentProviderProps) => {
  const [language, setLanguage] = useState(defaultLanguage);
  const [isEditMode, setEditMode] = useState(enableEditMode);
  const [subscribedIdentifiers, setSubscribedIdentifiers] = useState<string[]>([]);
  const [contentCache, setContentCache] = useState<Record<string, any>>({});

  // Get auth info to optimize for non-editors
  const { canEditContent, isLoaded } = useAuth();

  // Only enable edit mode if user can actually edit content
  const effectiveEditMode = isEditMode && canEditContent;

  // Update language when defaultLanguage prop changes
  useEffect(() => {
    setLanguage(defaultLanguage);
  }, [defaultLanguage]);

  // Subscribe to content updates for real-time changes
  const subscription = useContentSubscription(subscribedIdentifiers, language);

  // Update cache when subscription data changes
  useEffect(() => {
    if (subscription.content) {
      setContentCache(prev => ({
        ...prev,
        ...subscription.content,
      }));
    }
  }, [subscription.content]);

  // Language change handler
  const handleLanguageChange = (newLanguage: string) => {
    setLanguage(newLanguage);
    // Clear cache when language changes to force refetch
    setContentCache({});
  };

  // Refresh content handler
  const refreshContent = (identifiers?: string[]) => {
    if (identifiers) {
      // Add new identifiers to subscription
      setSubscribedIdentifiers(prev => {
        const newIdentifiers = identifiers.filter(id => !prev.includes(id));
        return [...prev, ...newIdentifiers];
      });
    } else {
      // Clear cache to force refetch all content
      setContentCache({});
    }
  };

  const value: ContentContextType = {
    language,
    setLanguage: handleLanguageChange,
    isEditMode: effectiveEditMode,
    setEditMode: (editMode: boolean) => {
      // Only allow setting edit mode if user can edit content
      if (canEditContent) {
        setEditMode(editMode);
      }
    },
    contentCache,
    refreshContent,
  };

  return (
    <ContentContext.Provider value={value}>
      {children}
    </ContentContext.Provider>
  );
};

export const useContentContext = () => {
  const context = useContext(ContentContext);
  if (!context) {
    throw new Error("useContentContext must be used within a ContentProvider");
  }
  return context;
};

// Enhanced DynamicContent that uses the context
export const ContextualDynamicContent = ({ 
  identifier, 
  fallback,
  className,
  children 
}: {
  identifier: string;
  fallback?: ReactNode;
  className?: string;
  children?: (content: any, contentType: any) => ReactNode;
}) => {
  const { language, isEditMode, contentCache, refreshContent } = useContentContext();
  
  // Add this identifier to the subscription
  useEffect(() => {
    refreshContent([identifier]);
  }, [identifier, refreshContent]);

  const content = contentCache[identifier];

  if (!content) {
    if (fallback) {
      return <div className={className}>{fallback}</div>;
    }
    return null;
  }

  // In edit mode, add edit overlay
  if (isEditMode) {
    return (
      <div className={`relative group ${className}`}>
        <EditOverlay identifier={identifier} contentType={content.contentType?.name} />
        {children ? children(content, content.contentType) : <div>Content: {identifier}</div>}
      </div>
    );
  }

  return (
    <div className={className}>
      {children ? children(content, content.contentType) : <div>Content: {identifier}</div>}
    </div>
  );
};

// Edit overlay for edit mode
const EditOverlay = ({ identifier, contentType }: { identifier: string; contentType?: string }) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [contentId, setContentId] = useState<string | null>(null);

  // Get content ID by identifier and language
  const { language } = useContentContext();
  const { content } = useContent(identifier, language);

  const handleEdit = () => {
    if (content?._id) {
      setContentId(content._id);
      setIsEditModalOpen(true);
    } else {
      // If content doesn't exist, we could create it or show a message
      console.log(`Content not found for identifier: ${identifier}`);
      // TODO: Implement content creation flow
    }
  };

  const handlePreview = () => {
    if (content?._id) {
      setContentId(content._id);
      setIsPreviewModalOpen(true);
    }
  };

  return (
    <>
      <div className="absolute inset-0 bg-blue-500 bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity z-10 flex items-center justify-center">
        <div className="flex space-x-2">
          <button
            onClick={handleEdit}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <span>✏️</span>
            <span>Edit</span>
          </button>
          {content && (
            <button
              onClick={handlePreview}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
            >
              <span>👁️</span>
              <span>Preview</span>
            </button>
          )}
        </div>
      </div>

      {/* Edit Modal */}
      {contentId && (
        <ContentEditModal
          contentId={contentId as any}
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setContentId(null);
          }}
          onSave={() => {
            setIsEditModalOpen(false);
            setContentId(null);
            // Refresh content
            window.location.reload(); // Simple refresh for now
          }}
        />
      )}

      {/* Preview Modal */}
      {contentId && (
        <ContentPreviewModal
          contentId={contentId as any}
          isOpen={isPreviewModalOpen}
          onClose={() => {
            setIsPreviewModalOpen(false);
            setContentId(null);
          }}
        />
      )}
    </>
  );
};

// Language switcher component
export const LanguageSwitcher = ({ className }: { className?: string }) => {
  const { language, setLanguage } = useContentContext();

  const languages = [
    { code: "en", name: "English", flag: "🇺🇸" },
    { code: "es", name: "Español", flag: "🇪🇸" },
  ];

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {languages.map((lang) => (
        <button
          key={lang.code}
          onClick={() => setLanguage(lang.code)}
          className={`flex items-center space-x-1 px-3 py-1 rounded-lg transition-colors ${
            language === lang.code
              ? "bg-blue-600 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          }`}
        >
          <span>{lang.flag}</span>
          <span className="text-sm font-medium">{lang.code.toUpperCase()}</span>
        </button>
      ))}
    </div>
  );
};

// Edit mode toggle (for admin users)
export const EditModeToggle = ({ className }: { className?: string }) => {
  const { isEditMode, setEditMode } = useContentContext();
  const { canEditContent } = useAuth();

  // Don't render if user can't edit content
  if (!canEditContent) {
    return null;
  }

  return (
    <button
      onClick={() => setEditMode(!isEditMode)}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
        isEditMode
          ? "bg-orange-600 text-white"
          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
      } ${className}`}
    >
      <span>{isEditMode ? "🔧" : "👁️"}</span>
      <span className="text-sm font-medium">
        {isEditMode ? "Exit Edit Mode" : "Edit Mode"}
      </span>
    </button>
  );
};

// Content analytics tracker
export const ContentAnalytics = () => {
  const { language, contentCache } = useContentContext();

  useEffect(() => {
    // Track page view
    const trackPageView = async () => {
      try {
        // This would call your analytics API
        await fetch("/api/analytics/pageview", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            path: window.location.pathname,
            language,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            referrer: document.referrer,
          }),
        });
      } catch (error) {
        console.error("Failed to track page view:", error);
      }
    };

    trackPageView();
  }, [language]);

  // Track content interactions
  useEffect(() => {
    const trackContentView = async (identifier: string) => {
      try {
        await fetch("/api/analytics/content-view", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            identifier,
            language,
            timestamp: Date.now(),
          }),
        });
      } catch (error) {
        console.error("Failed to track content view:", error);
      }
    };

    // Track when content is loaded
    Object.keys(contentCache).forEach(identifier => {
      trackContentView(identifier);
    });
  }, [contentCache, language]);

  return null; // This component doesn't render anything
};

// Performance monitoring for content loading
export const ContentPerformanceMonitor = () => {
  const { contentCache } = useContentContext();

  useEffect(() => {
    // Monitor content loading performance
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes("content-load")) {
          console.log(`Content load time: ${entry.duration}ms for ${entry.name}`);
        }
      });
    });

    observer.observe({ entryTypes: ["measure"] });

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    // Mark content load completion
    Object.keys(contentCache).forEach(identifier => {
      performance.mark(`content-load-${identifier}-end`);
      try {
        performance.measure(
          `content-load-${identifier}`,
          `content-load-${identifier}-start`,
          `content-load-${identifier}-end`
        );
      } catch (error) {
        // Start mark might not exist, ignore
      }
    });
  }, [contentCache]);

  return null;
};
