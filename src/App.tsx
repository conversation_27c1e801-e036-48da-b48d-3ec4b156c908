
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ClerkProvider } from "@clerk/clerk-react";
import { ConvexProvider, ConvexReactClient } from "convex/react";
import { I18nProvider } from "@/contexts/I18nContext";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { SettingsProvider } from "@/contexts/SettingsContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { AdminLayout } from "@/components/admin/AdminLayout";
import Index from "./pages/Index";
import Services from "./pages/Services";
import About from "./pages/About";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import { NetworkSolutions } from "./pages/NetworkSolutions";
import { NationalConnectivity } from "./pages/NationalConnectivity";
import { BranchOfficeConnectivity } from "./pages/BranchOfficeConnectivity";
import { ManagedEnterpriseInternet } from "./pages/ManagedEnterpriseInternet";
import DomesticServices from "./pages/DomesticServices";
import TrainingPrograms from "./pages/TrainingPrograms";
import ProductReselling from "./pages/ProductReselling";
import { AdminDashboard } from "./pages/admin/Dashboard";
import { AnalyticsDashboard } from "./components/admin/analytics/AnalyticsDashboard";
import { SiteSettingsManager } from "./components/admin/settings/SiteSettingsManager";
import { TranslationManager } from "./components/admin/translations/TranslationManager";
import { ContactFormManager } from "./components/admin/contacts/ContactFormManager";
import { ContentManagement } from "./components/admin/content/ContentManagement";
import { MediaLibrary } from "./components/admin/media/MediaLibrary";
import { UserManagement } from "./components/admin/users/UserManagement";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { CMSSetup } from "./components/admin/setup/CMSSetup";
import { AutoCMSInitializer } from "./components/AutoCMSInitializer";
import { CMSStatusNotification } from "./components/CMSStatusNotification";

const queryClient = new QueryClient();

// Initialize Convex client
const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL || "");

// Get Clerk publishable key
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!clerkPubKey) {
  throw new Error("Missing Clerk Publishable Key");
}

const App = () => (
  <ClerkProvider publishableKey={clerkPubKey}>
    <ConvexProvider client={convex}>
      <QueryClientProvider client={queryClient}>
        <AutoCMSInitializer>
          <AuthProvider>
            <SettingsProvider>
              <I18nProvider>
                <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Index />} />
                <Route path="/services" element={<Services />} />
                <Route path="/about" element={<About />} />
                <Route path="/contact" element={<Contact />} />

                {/* Network Solutions Routes */}
                <Route path="/network-solutions" element={<NetworkSolutions />} />
                <Route path="/network-solutions/national-connectivity" element={<NationalConnectivity />} />
                <Route path="/network-solutions/branch-office" element={<BranchOfficeConnectivity />} />
                <Route path="/network-solutions/managed-internet" element={<ManagedEnterpriseInternet />} />

                {/* Service Pages */}
                <Route path="/domestic-services" element={<DomesticServices />} />
                <Route path="/training-programs" element={<TrainingPrograms />} />
                <Route path="/product-reselling" element={<ProductReselling />} />

                {/* Admin Routes */}
                <Route path="/admin" element={
                  <ProtectedRoute requiredRole="content_editor">
                    <AdminLayout />
                  </ProtectedRoute>
                }>
                  <Route index element={<AdminDashboard />} />
                  <Route path="content" element={<ContentManagement />} />
                  <Route path="media" element={<MediaLibrary />} />
                  <Route path="users" element={
                    <ProtectedRoute requiredRole="admin">
                      <ErrorBoundary>
                        <UserManagement />
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />
                  <Route path="analytics" element={<AnalyticsDashboard />} />
                  <Route path="settings" element={<SiteSettingsManager />} />
                  <Route path="translations" element={<TranslationManager />} />
                  <Route path="contacts" element={<ContactFormManager />} />
                  <Route path="setup" element={<CMSSetup />} />
                  {/* Additional admin routes will be added here */}
                </Route>

                {/* Catch-all route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
              </TooltipProvider>
            </I18nProvider>
          </SettingsProvider>
        </AuthProvider>
        </AutoCMSInitializer>
      </QueryClientProvider>
    </ConvexProvider>
  </ClerkProvider>
);

export default App;
