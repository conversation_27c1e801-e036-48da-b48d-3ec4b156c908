import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

// Hook for getting single content item
export const useContent = (identifier: string, language?: string, includeDraft?: boolean) => {
  const content = useQuery(
    api.content.getContent,
    { identifier, language, includeDraft }
  );

  return {
    content,
    isLoading: content === undefined,
    error: content === null ? "Content not found" : null,
  };
};

// Hook for getting multiple content items
export const useContentBatch = (identifiers: string[], language?: string, includeDraft?: boolean) => {
  const contentBatch = useQuery(
    api.content.getContentBatch,
    { identifiers, language, includeDraft }
  );

  return {
    content: contentBatch,
    isLoading: contentBatch === undefined,
  };
};

// Hook for getting all content with pagination
export const useAllContent = (options?: {
  language?: string;
  status?: "draft" | "published";
  limit?: number;
  cursor?: string;
}) => {
  const content = useQuery(api.content.getAllContent, options);

  return {
    content,
    isLoading: content === undefined,
  };
};

// Hook for content search
export const useContentSearch = (searchQuery: string, options?: {
  language?: string;
  contentType?: string;
  status?: "draft" | "published";
  limit?: number;
}) => {
  const results = useQuery(
    api.content.searchContent,
    searchQuery ? { query: searchQuery, ...options } : "skip"
  );

  return {
    results,
    isLoading: results === undefined,
  };
};

// Hook for content history
export const useContentHistory = (identifier: string, language: string, limit?: number) => {
  const history = useQuery(
    api.content.getContentHistory,
    { identifier, language, limit }
  );

  return {
    history,
    isLoading: history === undefined,
  };
};

// Hook for content statistics
export const useContentStats = (language?: string) => {
  const stats = useQuery(api.content.getContentStats, { language });

  return {
    stats,
    isLoading: stats === undefined,
  };
};

// Hook for content mutations
export const useContentMutations = () => {
  const upsertContent = useMutation(api.content.upsertContent);
  const publishContent = useMutation(api.content.publishContent);
  const unpublishContent = useMutation(api.content.unpublishContent);
  const duplicateContent = useMutation(api.content.duplicateContent);
  const revertContent = useMutation(api.content.revertContent);
  const bulkUpdateContent = useMutation(api.content.bulkUpdateContent);

  return {
    upsertContent,
    publishContent,
    unpublishContent,
    duplicateContent,
    revertContent,
    bulkUpdateContent,
  };
};

// Utility hook for managing content state
export const useContentManager = (identifier: string, language: string = "en") => {
  const { content, isLoading } = useContent(identifier, language, true);
  const { history } = useContentHistory(identifier, language);
  const mutations = useContentMutations();

  const publish = async (publishNote?: string) => {
    return await mutations.publishContent({
      identifier,
      language,
      publishNote,
    });
  };

  const unpublish = async (unpublishNote?: string) => {
    return await mutations.unpublishContent({
      identifier,
      language,
      unpublishNote,
    });
  };

  const save = async (data: any, status?: "draft" | "published") => {
    if (!content?.contentType) {
      throw new Error("Content type not found");
    }

    return await mutations.upsertContent({
      identifier,
      language,
      data,
      contentTypeId: content.contentTypeId,
      status,
    });
  };

  const duplicate = async (toLanguage: string, status?: "draft" | "published") => {
    return await mutations.duplicateContent({
      identifier,
      fromLanguage: language,
      toLanguage,
      status,
    });
  };

  const revert = async (targetVersion: number, revertNote?: string) => {
    return await mutations.revertContent({
      identifier,
      language,
      targetVersion,
      revertNote,
    });
  };

  return {
    content,
    history,
    isLoading,
    isDraft: content?.status === "draft",
    isPublished: content?.status === "published",
    actions: {
      save,
      publish,
      unpublish,
      duplicate,
      revert,
    },
  };
};

// Hook for real-time content updates
export const useContentSubscription = (identifiers: string[], language?: string) => {
  const content = useContentBatch(identifiers, language);
  
  // This will automatically re-run when content changes due to Convex's real-time nature
  return content;
};

// Utility functions
export const getContentValue = (content: any, fieldName: string, defaultValue: any = null) => {
  return content?.data?.[fieldName] ?? defaultValue;
};

export const hasContentChanged = (content1: any, content2: any): boolean => {
  if (!content1 || !content2) return true;
  return JSON.stringify(content1.data) !== JSON.stringify(content2.data);
};

export const getContentPreview = (content: any, maxLength: number = 100): string => {
  if (!content?.data) return "";
  
  // Try to find a text field to use as preview
  const textFields = ["title", "content", "description", "subtitle"];
  
  for (const field of textFields) {
    const value = content.data[field];
    if (typeof value === "string" && value.length > 0) {
      return value.length > maxLength 
        ? value.substring(0, maxLength) + "..."
        : value;
    }
  }
  
  // Fallback to stringified data
  const dataString = JSON.stringify(content.data);
  return dataString.length > maxLength 
    ? dataString.substring(0, maxLength) + "..."
    : dataString;
};

// Content validation utilities
export const validateContentStructure = (content: any, contentType: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!content?.data) {
    errors.push("Content data is missing");
    return { isValid: false, errors };
  }
  
  if (!contentType?.fields) {
    errors.push("Content type fields are missing");
    return { isValid: false, errors };
  }
  
  // Check required fields
  for (const field of contentType.fields) {
    if (field.required) {
      const value = content.data[field.name];
      if (value === undefined || value === null || value === "") {
        errors.push(`Required field "${field.label}" is missing`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};
