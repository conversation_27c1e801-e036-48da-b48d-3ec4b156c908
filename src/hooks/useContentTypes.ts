import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { ContentType, FieldDefinition } from "@/types/content";

export const useContentTypes = () => {
  const contentTypes = useQuery(api.contentTypes.getAllContentTypes);
  const createContentType = useMutation(api.contentTypes.createContentType);
  const updateContentType = useMutation(api.contentTypes.updateContentType);
  const deleteContentType = useMutation(api.contentTypes.deleteContentType);
  const initializePredefined = useMutation(api.contentTypes.initializePredefinedContentTypes);

  return {
    contentTypes,
    createContentType,
    updateContentType,
    deleteContentType,
    initializePredefined,
    isLoading: contentTypes === undefined,
  };
};

export const useContentType = (name: string) => {
  const contentType = useQuery(api.contentTypes.getContentTypeByName, { name });
  
  return {
    contentType,
    isLoading: contentType === undefined,
  };
};

export const useContentTypesByCategory = (category: string) => {
  const contentTypes = useQuery(api.contentTypes.getContentTypesByCategory, { category });
  
  return {
    contentTypes,
    isLoading: contentTypes === undefined,
  };
};

// Utility functions for working with content types
export const getFieldByName = (contentType: ContentType, fieldName: string): FieldDefinition | undefined => {
  return contentType.fields.find(field => field.name === fieldName);
};

export const getDefaultValueForField = (field: FieldDefinition): any => {
  if (field.defaultValue !== undefined) {
    return field.defaultValue;
  }

  switch (field.type) {
    case "text":
    case "richText":
    case "email":
    case "url":
    case "color":
      return "";
    case "number":
      return 0;
    case "boolean":
      return false;
    case "array":
      return [];
    case "object":
      return {};
    case "select":
      return field.validation?.options?.[0] || "";
    case "multiSelect":
      return [];
    case "date":
      return new Date().toISOString();
    case "image":
      return null;
    default:
      return null;
  }
};

export const createDefaultContentData = (contentType: ContentType): Record<string, any> => {
  const data: Record<string, any> = {};
  
  for (const field of contentType.fields) {
    data[field.name] = getDefaultValueForField(field);
  }
  
  return data;
};

export const validateFieldValue = (field: FieldDefinition, value: any): { isValid: boolean; error?: string } => {
  // Check required fields
  if (field.required && (value === undefined || value === null || value === "")) {
    return { isValid: false, error: `${field.label} is required` };
  }

  // Skip validation if field is not required and empty
  if (!field.required && (value === undefined || value === null || value === "")) {
    return { isValid: true };
  }

  // Type-specific validation
  switch (field.type) {
    case "number":
      if (typeof value !== "number") {
        return { isValid: false, error: `${field.label} must be a number` };
      }
      if (field.validation?.min !== undefined && value < field.validation.min) {
        return { isValid: false, error: `${field.label} must be at least ${field.validation.min}` };
      }
      if (field.validation?.max !== undefined && value > field.validation.max) {
        return { isValid: false, error: `${field.label} must be at most ${field.validation.max}` };
      }
      break;

    case "email":
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return { isValid: false, error: `${field.label} must be a valid email address` };
      }
      break;

    case "url":
      try {
        new URL(value);
      } catch {
        return { isValid: false, error: `${field.label} must be a valid URL` };
      }
      break;

    case "select":
      if (field.validation?.options && !field.validation.options.includes(value)) {
        return { isValid: false, error: `${field.label} must be one of: ${field.validation.options.join(", ")}` };
      }
      break;

    case "multiSelect":
      if (field.validation?.options && Array.isArray(value)) {
        const invalidOptions = value.filter(v => !field.validation?.options?.includes(v));
        if (invalidOptions.length > 0) {
          return { isValid: false, error: `${field.label} contains invalid options: ${invalidOptions.join(", ")}` };
        }
      }
      break;
  }

  return { isValid: true };
};

export const validateContentData = (contentType: ContentType, data: Record<string, any>): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  for (const field of contentType.fields) {
    const validation = validateFieldValue(field, data[field.name]);
    if (!validation.isValid && validation.error) {
      errors[field.name] = validation.error;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// Content type categories
export const CONTENT_TYPE_CATEGORIES = [
  { value: "general", label: "General" },
  { value: "sections", label: "Page Sections" },
  { value: "content", label: "Content Blocks" },
  { value: "media", label: "Media" },
  { value: "services", label: "Services" },
  { value: "social", label: "Social" },
  { value: "contact", label: "Contact" },
  { value: "navigation", label: "Navigation" },
  { value: "forms", label: "Forms" },
] as const;

// Field type options for form building
export const FIELD_TYPE_OPTIONS = [
  { value: "text", label: "Text", description: "Single line text input" },
  { value: "richText", label: "Rich Text", description: "Multi-line text with formatting" },
  { value: "number", label: "Number", description: "Numeric input" },
  { value: "boolean", label: "Boolean", description: "True/false checkbox" },
  { value: "select", label: "Select", description: "Dropdown selection" },
  { value: "multiSelect", label: "Multi Select", description: "Multiple choice selection" },
  { value: "date", label: "Date", description: "Date picker" },
  { value: "email", label: "Email", description: "Email address input" },
  { value: "url", label: "URL", description: "Website URL input" },
  { value: "color", label: "Color", description: "Color picker" },
  { value: "image", label: "Image", description: "Image upload" },
  { value: "array", label: "Array", description: "List of items" },
  { value: "object", label: "Object", description: "Structured data" },
] as const;
